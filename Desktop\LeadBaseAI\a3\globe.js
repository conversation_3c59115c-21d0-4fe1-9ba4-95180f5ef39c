// Scene
const scene = new THREE.Scene();

// Camera
const camera = new THREE.PerspectiveCamera(
  50,
  window.innerWidth / window.innerHeight,
  0.1,
  1000
);
camera.position.z = 3.4;

// Renderer
const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
renderer.setSize(window.innerWidth, window.innerHeight);
document.getElementById("globe-container").appendChild(renderer.domElement);

// Globe (wireframe sphere)
const globeGeometry = new THREE.SphereGeometry(1, 32, 32);
const globeMaterial = new THREE.MeshBasicMaterial({
  color: 0x00aaff,
  wireframe: true
});
const globe = new THREE.Mesh(globeGeometry, globeMaterial);
scene.add(globe);

// Subtle particles around the globe
const particlesGeometry = new THREE.BufferGeometry();
const particleCount = 200;
const positions = new Float32Array(particleCount * 3);
for (let i = 0; i < particleCount * 3; i++) {
  positions[i] = (Math.random() - 0.5) * 6;
}
particlesGeometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
const particlesMaterial = new THREE.PointsMaterial({
  color: 0x00aaff,
  size: 0.02,
  transparent: true,
  opacity: 0.6
});
const particles = new THREE.Points(particlesGeometry, particlesMaterial);
scene.add(particles);

// Animation loop
function animate() {
  requestAnimationFrame(animate);

  globe.rotation.y += 0.003;
  particles.rotation.y += 0.002;

  renderer.render(scene, camera);
}
animate();

// Resize handling
window.addEventListener("resize", () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
});

// --- Smooth Scroll Effect ---
let ticking = false;

function updateGlobePosition() {
  const scrollY = window.scrollY;
  const windowHeight = window.innerHeight;
  const container = document.getElementById("globe-container");
  
  const currentSection = Math.floor(scrollY / windowHeight) + 1;
  let transform = "translate(-50%, -50%) scale(1)";
  
  if (currentSection === 2) {
    transform = "translate(-90%, -50%) scale(0.5)"; // Leftmost, above background
  } else if (currentSection === 3) {
    transform = "translate(-10%, -50%) scale(0.5)"; // Right from left
  }
  
  container.style.transform = transform;
  container.style.zIndex = (currentSection > 1) ? 3 : 1; // Ensure visibility above bg
  
  ticking = false;
}

function requestTick() {
  if (!ticking) {
    requestAnimationFrame(updateGlobePosition);
    ticking = true;
  }
}

window.addEventListener("scroll", requestTick, { passive: true });