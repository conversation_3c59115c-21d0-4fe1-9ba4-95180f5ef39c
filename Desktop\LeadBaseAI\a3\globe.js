// Scene
const scene = new THREE.Scene();

// Camera
const camera = new THREE.PerspectiveCamera(
  50,
  window.innerWidth / window.innerHeight,
  0.1,
  1000
);
camera.position.z = 3.4;

// Renderer
const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
renderer.setSize(window.innerWidth, window.innerHeight);
const globeContainer = document.getElementById("globe-container");
globeContainer.appendChild(renderer.domElement);
// Ensure globe stays above all section backgrounds
globeContainer.style.zIndex = "9999";

// Globe (wireframe sphere)
const globeGeometry = new THREE.SphereGeometry(1, 32, 32);
const globeMaterial = new THREE.MeshBasicMaterial({
  color: 0x00aaff,
  wireframe: true,
});
const globe = new THREE.Mesh(globeGeometry, globeMaterial);
scene.add(globe);

// Subtle particles around the globe
const particlesGeometry = new THREE.BufferGeometry();
const particleCount = 200;
const positions = new Float32Array(particleCount * 3);
for (let i = 0; i < particleCount * 3; i++) {
  positions[i] = (Math.random() - 0.5) * 6;
}
particlesGeometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
const particlesMaterial = new THREE.PointsMaterial({
  color: 0x00aaff,
  size: 0.02,
  transparent: true,
  opacity: 0.6,
});
const particles = new THREE.Points(particlesGeometry, particlesMaterial);
scene.add(particles);

// Define target values for smooth transitions
const target = {
  globeX: 0,
  globeScale: 1,
};
const lerpFactor = 0.12; // Higher = more responsive, lower = more floaty/smooth

// Animation loop
function animate() {
  requestAnimationFrame(animate);

  // Smoothly move towards the target values
  globe.position.x += (target.globeX - globe.position.x) * lerpFactor;
  globe.scale.x += (target.globeScale - globe.scale.x) * lerpFactor;
  globe.scale.y += (target.globeScale - globe.scale.y) * lerpFactor;
  globe.scale.z += (target.globeScale - globe.scale.z) * lerpFactor;

  globe.rotation.y += 0.003;
  particles.rotation.y += 0.002;

  renderer.render(scene, camera);
}
animate();

// Resize handling
window.addEventListener("resize", () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
});

// --- Smooth Scroll Effect ---
let ticking = false;

function updateTargetValues() {
  const scrollY = window.scrollY;
  const windowHeight = window.innerHeight;

  const xLeft = 1.6;  // how far to move globe to left
  const xRight = 1.6; // how far to move globe to right

  // Smooth interpolation function
  const lerp = (start, end, progress) => start + (end - start) * progress;

  if (scrollY < windowHeight) {
    // Section 1 (0vh-100vh): Stay centered, but start transitioning near end
    const progress = scrollY / windowHeight; // 0 to 1
    target.globeX = lerp(0, -xLeft * 0.2, progress); // slight movement toward left
    target.globeScale = lerp(1, 0.85, progress); // slight scale down

  } else if (scrollY < 2 * windowHeight) {
    // Section 2 (100vh-200vh): Move from center to left smoothly
    const progress = (scrollY - windowHeight) / windowHeight; // 0 to 1
    target.globeX = lerp(-xLeft * 0.2, -xLeft, progress);
    target.globeScale = lerp(0.85, 0.7, progress);

  } else if (scrollY < 3 * windowHeight) {
    // Section 3 (200vh-300vh): Move from left to right smoothly
    const progress = (scrollY - 2 * windowHeight) / windowHeight; // 0 to 1
    target.globeX = lerp(-xLeft, xRight, progress);
    target.globeScale = 0.7; // keep same scale

  } else {
    // Section 4+ (300vh+): stay at right
    target.globeX = xRight;
    target.globeScale = 0.7;
  }

  ticking = false;
}

function requestTick() {
  if (!ticking) {
    requestAnimationFrame(updateTargetValues);
    ticking = true;
  }
}

window.addEventListener("scroll", requestTick, { passive: true });