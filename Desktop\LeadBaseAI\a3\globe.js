// Scene
const scene = new THREE.Scene();

// Camera
const camera = new THREE.PerspectiveCamera(
  50,
  window.innerWidth / window.innerHeight,
  0.1,
  1000
);
camera.position.z = 3.4;

// Renderer
const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
renderer.setSize(window.innerWidth, window.innerHeight);
const globeContainer = document.getElementById("globe-container");
globeContainer.appendChild(renderer.domElement);

// Globe (wireframe sphere)
const globeGeometry = new THREE.SphereGeometry(1, 32, 32);
const globeMaterial = new THREE.MeshBasicMaterial({
  color: 0x00aaff,
  wireframe: true,
});
const globe = new THREE.Mesh(globeGeometry, globeMaterial);
scene.add(globe);

// Subtle particles around the globe
const particlesGeometry = new THREE.BufferGeometry();
const particleCount = 200;
const positions = new Float32Array(particleCount * 3);
for (let i = 0; i < particleCount * 3; i++) {
  positions[i] = (Math.random() - 0.5) * 6;
}
particlesGeometry.setAttribute("position", new THREE.BufferAttribute(positions, 3));
const particlesMaterial = new THREE.PointsMaterial({
  color: 0x00aaff,
  size: 0.02,
  transparent: true,
  opacity: 0.6,
});
const particles = new THREE.Points(particlesGeometry, particlesMaterial);
scene.add(particles);

// Define target values for smooth transitions
const target = {
  cameraX: 0,
  globeScale: 1
};
const lerpFactor = 0.05; // Adjust this value for faster or slower transitions

// Animation loop
function animate() {
  requestAnimationFrame(animate);

  // Smoothly move towards the target values
  camera.position.x += (target.cameraX - camera.position.x) * lerpFactor;
  globe.scale.x += (target.globeScale - globe.scale.x) * lerpFactor;
  globe.scale.y += (target.globeScale - globe.scale.y) * lerpFactor;
  globe.scale.z += (target.globeScale - globe.scale.z) * lerpFactor;

  globe.rotation.y += 0.003;
  particles.rotation.y += 0.002;

  renderer.render(scene, camera);
}
animate();

// Resize handling
window.addEventListener("resize", () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
});

// --- Smooth Scroll Effect ---
let ticking = false;

function updateTargetValues() {
  const scrollY = window.scrollY;
  const windowHeight = window.innerHeight;
  const currentSection = Math.floor(scrollY / windowHeight) + 1;

  // Set the target values based on the current section
  if (currentSection === 1) {
    target.cameraX = 0;
    target.globeScale = 1;
  } else if (currentSection === 2) {
    target.cameraX = -1.5; // Adjusted value for a more "leftmost" appearance
    target.globeScale = 0.5;
  } else if (currentSection === 3) {
    target.cameraX = 1.5; // Adjusted value for a more "right" appearance
    target.globeScale = 0.5;
  }
  
  ticking = false;
}

function requestTick() {
  if (!ticking) {
    requestAnimationFrame(updateTargetValues);
    ticking = true;
  }
}

window.addEventListener("scroll", requestTick, { passive: true });