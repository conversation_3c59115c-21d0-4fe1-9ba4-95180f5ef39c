body {
    margin: 0;
    background: rgb(0, 0, 0);
    font-family: sans-serif;
    height: 700vh;
    overflow-x: hidden;
}

/* Hero section */
#hero-text {
    position: absolute;
    top: 35%;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: #ffffff;
    z-index: 2;
}

#hero-text h1 {
    font-size: 3rem;
    margin-bottom: 0.5rem;
}

#hero-text p {
    font-size: 1.2rem;
    opacity: 0.8;
}

/* Globe canvas wrapper */
#globe-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* This transform is necessary to center the fixed element */
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 999;
}

canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* Ensure sections are above or behind globe as needed */
section {
    position: relative;
    z-index: 0;
}